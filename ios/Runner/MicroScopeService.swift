import Foundation
import Network
import AudioToolbox
import UIKit

// Protocol for RTP Receiver Delegate
protocol RTPReceiverDelegate: AnyObject {
    func handleFrameData(_ data: Data)
}

// RTP Receiver Class
class RTPReceiver {
    weak var delegate: RTPReceiverDelegate?
    let port: UInt16
    let bufferSize = 65536  // Large buffer to prevent packet loss
    let udpReceiveQueue = DispatchQueue(label: "com.udp.receive", qos: .userInteractive, attributes: .concurrent)
    
    // Add detailed packet statistics
    private var totalPacketsReceived = 0
    private var totalBytesReceived: UInt64 = 0
    private var packetsPerSecond = 0
    private var bytesPerSecond: UInt64 = 0
    private var lastStatsTime = Date()
    private var consecutiveErrors = 0
    private var maxConsecutiveErrors = 0
    private var lastSuccessfulReceiveTime = Date()
    private var longestReceiveGap: TimeInterval = 0
    
    init(port: UInt16) {
        self.port = port
    }
    
    func startReceiving() {
        let startTime = Date()
        print("📡 [MICROSCOPE DEBUG] RTPReceiver startReceiving() called at \(startTime)")

        udpReceiveQueue.async { [weak self] in
            guard let self = self else { return }

            let asyncStartTime = Date()
            print("📡 [MICROSCOPE DEBUG] RTPReceiver async block started at \(asyncStartTime)")

            // Log network interfaces for debugging
            self.logNetworkInterfaces()
            print("📡 [MICROSCOPE DEBUG] Network interfaces logged at \(Date().timeIntervalSince(asyncStartTime))s")

            // Start periodic stats reporting
            self.startStatsReporting()
            print("📡 [MICROSCOPE DEBUG] Stats reporting started at \(Date().timeIntervalSince(asyncStartTime))s")

            print("📡 [MICROSCOPE DEBUG] Starting packet reception at \(Date().timeIntervalSince(asyncStartTime))s")
            self.receivePackets()
        }
    }
    
    private func logNetworkInterfaces() {
        var ifaddr: UnsafeMutablePointer<ifaddrs>?
        
        guard getifaddrs(&ifaddr) == 0 else {
            return
        }
        
        var interfaces = [String]()
        var currentInterface = ifaddr
        
        while let interface = currentInterface {
            let name = String(cString: interface.pointee.ifa_name)
            let family = interface.pointee.ifa_addr.pointee.sa_family
            
            if family == UInt8(AF_INET) || family == UInt8(AF_INET6) {
                var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                
                if getnameinfo(interface.pointee.ifa_addr, socklen_t(interface.pointee.ifa_addr.pointee.sa_len),
                               &hostname, socklen_t(hostname.count),
                               nil, 0, NI_NUMERICHOST) == 0 {
                    let address = String(cString: hostname)
                    interfaces.append("\(name): \(address) (family: \(family == UInt8(AF_INET) ? "IPv4" : "IPv6"))")
                }
            }
            
            currentInterface = interface.pointee.ifa_next
        }
        
        freeifaddrs(ifaddr)
    }
    
    private func startStatsReporting() {
        // Create a timer to report stats every 10 seconds
        let timer = Timer(timeInterval: 10.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            let now = Date()
            let elapsedTime = now.timeIntervalSince(self.lastStatsTime)
            
            // Reset periodic counters
            self.packetsPerSecond = 0
            self.bytesPerSecond = 0
            self.lastStatsTime = now
        }
        
        // Add the timer to the run loop
        RunLoop.main.add(timer, forMode: .common)
    }
    
    private func receivePackets() {
        let startTime = Date()
        print("📦 [MICROSCOPE DEBUG] receivePackets() started at \(startTime)")

        let socketFD = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP) // Create UDP socket
        guard socketFD >= 0 else {
            print("❌ [MICROSCOPE DEBUG] Failed to create socket")
            return
        }
        print("📦 [MICROSCOPE DEBUG] Socket created at \(Date().timeIntervalSince(startTime))s")

        // Set socket options to avoid dropped packets
        var bufferSize = 65536
        let socketResult = setsockopt(socketFD, SOL_SOCKET, SO_RCVBUF, &bufferSize, socklen_t(MemoryLayout<Int>.size))
        print("📦 [MICROSCOPE DEBUG] Socket buffer size set (result: \(socketResult)) at \(Date().timeIntervalSince(startTime))s")

        // Set socket timeout to detect connection issues
        var timeout = timeval(tv_sec: 5, tv_usec: 0) // 5 second timeout
        let timeoutResult = setsockopt(socketFD, SOL_SOCKET, SO_RCVTIMEO, &timeout, socklen_t(MemoryLayout<timeval>.size))
        print("📦 [MICROSCOPE DEBUG] Socket timeout set (result: \(timeoutResult)) at \(Date().timeIntervalSince(startTime))s")
        
        // Bind socket to UDP port
        var addr = sockaddr_in()
        addr.sin_family = sa_family_t(AF_INET)
        addr.sin_port = port.bigEndian
        addr.sin_addr.s_addr = inet_addr("************") // Use specific IP instead of INADDR_ANY

        let bindResult = withUnsafePointer(to: &addr) {
            $0.withMemoryRebound(to: sockaddr.self, capacity: 1) {
                bind(socketFD, $0, socklen_t(MemoryLayout<sockaddr_in>.size))
            }
        }

        guard bindResult >= 0 else {
            close(socketFD)
            return
        }

        print("📦 [MICROSCOPE DEBUG] Socket bound successfully, starting packet reception loop at \(Date().timeIntervalSince(startTime))s")

        // Start receiving packets in a continuous loop
        var buffer = [UInt8](repeating: 0, count: bufferSize)
        var packetCount = 0
        var errorCount = 0
        var totalBytes: UInt64 = 0
        var lastLogTime = Date()
        var firstPacketReceived = false

        lastStatsTime = Date()
        lastSuccessfulReceiveTime = Date()

        print("📦 [MICROSCOPE DEBUG] Entering receive loop at \(Date().timeIntervalSince(startTime))s")

        while true {
            let receivedBytes = recv(socketFD, &buffer, bufferSize, 0)
            let now = Date()

            if receivedBytes > 0 {
                // Log first packet
                if !firstPacketReceived {
                    firstPacketReceived = true
                    print("📦 [MICROSCOPE DEBUG] FIRST PACKET RECEIVED at \(now) - \(receivedBytes) bytes - total time: \(Date().timeIntervalSince(startTime))s")
                }
                // Reset error tracking on successful receive
                if consecutiveErrors > 0 {
                    consecutiveErrors = 0
                }
                
                // Track receive gap
                let gapTime = now.timeIntervalSince(lastSuccessfulReceiveTime)
                if gapTime > longestReceiveGap {
                    longestReceiveGap = gapTime
                }
                lastSuccessfulReceiveTime = now
                
                // Update statistics
                packetCount += 1
                totalPacketsReceived += 1
                packetsPerSecond += 1
                totalBytes += UInt64(receivedBytes)
                totalBytesReceived += UInt64(receivedBytes)
                bytesPerSecond += UInt64(receivedBytes)
                
                let packetData = Data(buffer.prefix(receivedBytes))
                
                lastLogTime = now
                totalBytes = 0
                
                self.handlePacket(packetData)
            } else if receivedBytes < 0 {
                // Track consecutive errors
                consecutiveErrors += 1
                if consecutiveErrors > maxConsecutiveErrors {
                    maxConsecutiveErrors = consecutiveErrors
                }
                
                errorCount += 1
                
                if errno == EAGAIN || errno == EWOULDBLOCK {
                    // Connection might be lost - log network status
                    if consecutiveErrors % 3 == 0 {
                        logNetworkInterfaces()
                    }
                }
                
                if errorCount > 100 || consecutiveErrors > 50 {
                    break
                }
                
                // Small delay to prevent CPU spinning on errors
                usleep(50000) // 50ms
            }
        }
        
        close(socketFD)
    }

    private func handlePacket(_ data: Data) {
        if data.count < 8 {
            return
        }
        
        DispatchQueue.main.async {
            self.delegate?.handleFrameData(data) // Notify delegate
        }
    }
}

// MicroScopeService Class
class MicroScopeService: RTPReceiverDelegate {
    static let shared = MicroScopeService()
    private var rtpReceiver: RTPReceiver?
    private var heartbeatTimer: Timer?
    private var frameTimeoutTimer: Timer?
    private var connection: NWConnection?
    private var listener: NWListener?
    private var isListenerStarting = false
    weak var methodChannel: FlutterMethodChannel?

    private let host = NWEndpoint.Host("************")
    private let sendPort: NWEndpoint.Port = 20000
    private let receivePort: UInt16 = 10900
    private let commandReceiveQueue = DispatchQueue(label: "CommandReceiveQueue", qos: .userInteractive, attributes: .concurrent)
    
    private var currentFrameIndex = -1
    private var expectedPacketIndex = 0
    private var currentFrameData = Data()
    private let maxPacketSize = 1450
    private var lastFrameReceivedTime: Date?
    private let frameTimeoutInterval: TimeInterval = 5.0
    private var hasReportedTimeout = false
    private var frameCount = 0
    private var lastFrameProcessTime = Date()

    private init() {
        initializeConnection()
    }

    func refreshConnection() {
        let startTime = Date()
        print("🔄 [MICROSCOPE DEBUG] refreshConnection() started at \(startTime)")
        print("🔄 [MICROSCOPE DEBUG] Refreshing connection - cleaning up existing connections")

        // Cancel existing connection
        connection?.cancel()
        connection = nil

        // Cancel command listener with proper cleanup
        listener?.cancel()
        listener = nil
        isListenerStarting = false

        // Clean up receiver
        rtpReceiver = nil

        // Invalidate all timers
        heartbeatTimer?.invalidate()
        heartbeatTimer = nil
        frameTimeoutTimer?.invalidate()
        frameTimeoutTimer = nil

        // Reset all state variables
        currentFrameIndex = -1
        expectedPacketIndex = 0
        currentFrameData = Data()
        lastFrameReceivedTime = nil
        hasReportedTimeout = false

        print("🔄 [MICROSCOPE DEBUG] Cleanup completed at \(Date().timeIntervalSince(startTime))s")

        // Wait longer to ensure all network resources are released
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            guard let self = self else { return }
            print("🔄 [MICROSCOPE DEBUG] Reinitializing connection after cleanup at \(Date().timeIntervalSince(startTime))s")
            self.initializeConnection()
        }
    }

    // Complete reset is more thorough, performing a complete teardown and rebuild
    func completeReset() {
        // Cancel and clear connection
        connection?.cancel()
        connection = nil

        // Cancel command listener
        listener?.cancel()
        listener = nil
        isListenerStarting = false

        // Clean up receiver
        rtpReceiver = nil
        
        // Invalidate all timers
        heartbeatTimer?.invalidate()
        heartbeatTimer = nil
        frameTimeoutTimer?.invalidate()
        frameTimeoutTimer = nil
        
        // Reset all state variables
        currentFrameIndex = -1
        expectedPacketIndex = 0
        currentFrameData = Data()
        lastFrameReceivedTime = nil
        hasReportedTimeout = false
        
        // Send final command to potential device to turn it off
        // Using a new temporary connection for this final command
        let tempConnection = NWConnection(host: host, port: sendPort, using: .udp)
        tempConnection.start(queue: .main)
        
        // Send shutdown command
        let shutdownCmd = Data([0x4A, 0x48, 0x43, 0x4D, 0x44, 0xD0, 0x02])
        tempConnection.send(content: shutdownCmd, completion: .contentProcessed({ error in
            // Cancel temp connection after sending
            tempConnection.cancel()
            
            // Wait longer before reconnecting to ensure network resources are fully released
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                guard let self = self else { return }
                self.initializeConnection()
            }
        }))
    }

    private func initializeConnection() {
        let startTime = Date()
        print("🔄 [MICROSCOPE DEBUG] Starting connection initialization at \(startTime)")

        setupConnection()
        print("🔄 [MICROSCOPE DEBUG] Connection setup completed in \(Date().timeIntervalSince(startTime))s")

        setupReceiver()
        print("🔄 [MICROSCOPE DEBUG] Receiver setup completed in \(Date().timeIntervalSince(startTime))s")

        startUDPListener()
        print("🔄 [MICROSCOPE DEBUG] UDP listener started in \(Date().timeIntervalSince(startTime))s")

        sendInitialCommands()
        print("🔄 [MICROSCOPE DEBUG] Initial commands sent in \(Date().timeIntervalSince(startTime))s")

        startHeartbeat()
        print("🔄 [MICROSCOPE DEBUG] Heartbeat started in \(Date().timeIntervalSince(startTime))s")

        startFrameTimeoutMonitor()
        print("🔄 [MICROSCOPE DEBUG] Frame timeout monitor started in \(Date().timeIntervalSince(startTime))s")
        print("✅ [MICROSCOPE DEBUG] Full initialization completed in \(Date().timeIntervalSince(startTime))s")
    }

    private func setupConnection() {
        let startTime = Date()
        print("🌐 [MICROSCOPE DEBUG] Setting up connection to \(host):\(sendPort)")

        connection = NWConnection(host: host, port: sendPort, using: .udp)

        connection?.stateUpdateHandler = { [weak self] state in
            guard let self = self else { return }
            let elapsed = Date().timeIntervalSince(startTime)

            switch state {
            case .setup:
                print("🌐 [MICROSCOPE DEBUG] Connection setup state at \(elapsed)s")
            case .waiting(let error):
                print("🌐 [MICROSCOPE DEBUG] Connection waiting state at \(elapsed)s: \(error)")
            case .preparing:
                print("🌐 [MICROSCOPE DEBUG] Connection preparing state at \(elapsed)s")
            case .ready:
                print("✅ [MICROSCOPE DEBUG] Connection ready at \(elapsed)s")
            case .failed(let error):
                print("❌ [MICROSCOPE DEBUG] Connection failed at \(elapsed)s: \(error)")
            case .cancelled:
                print("🚫 [MICROSCOPE DEBUG] Connection cancelled at \(elapsed)s")
            @unknown default:
                print("❓ [MICROSCOPE DEBUG] Unknown connection state at \(elapsed)s")
            }
        }

        connection?.start(queue: .main)
        print("🌐 [MICROSCOPE DEBUG] Connection start() called at \(Date().timeIntervalSince(startTime))s")
    }

    private func setupReceiver() {
        let startTime = Date()
        print("📡 [MICROSCOPE DEBUG] Setting up RTP receiver on port \(receivePort)")

        rtpReceiver = RTPReceiver(port: receivePort)
        print("📡 [MICROSCOPE DEBUG] RTP receiver created at \(Date().timeIntervalSince(startTime))s")

        rtpReceiver?.delegate = self
        print("📡 [MICROSCOPE DEBUG] RTP receiver delegate set at \(Date().timeIntervalSince(startTime))s")

        rtpReceiver?.startReceiving()
        print("📡 [MICROSCOPE DEBUG] RTP receiver started at \(Date().timeIntervalSince(startTime))s")
    }

    private func startUDPListener() {
        let startTime = Date()
        print("🎧 [MICROSCOPE DEBUG] Starting UDP listener setup")

        // Prevent multiple listeners from being created
        guard !isListenerStarting else {
            print("🎧 [MICROSCOPE DEBUG] UDP listener already starting, skipping")
            return
        }

        // Cancel any existing listener first
        listener?.cancel()
        listener = nil
        isListenerStarting = true
        print("🎧 [MICROSCOPE DEBUG] Previous listener cancelled, starting new one at \(Date().timeIntervalSince(startTime))s")

        commandReceiveQueue.async { [weak self] in
            guard let self = self else {
                self?.isListenerStarting = false
                return
            }
            do {
                let asyncStartTime = Date()
                print("🎧 [MICROSCOPE DEBUG] Starting UDP command listener on port \(self.sendPort) at \(asyncStartTime)")
                self.listener = try NWListener(using: .udp, on: self.sendPort)
                print("🎧 [MICROSCOPE DEBUG] NWListener created in \(Date().timeIntervalSince(asyncStartTime))s")

                self.listener?.stateUpdateHandler = { [weak self] state in
                    let elapsed = Date().timeIntervalSince(asyncStartTime)
                    switch state {
                    case .setup:
                        print("🎧 [MICROSCOPE DEBUG] UDP listener setup at \(elapsed)s")
                    case .waiting(let error):
                        print("🎧 [MICROSCOPE DEBUG] UDP listener waiting at \(elapsed)s: \(error)")
                    case .ready:
                        print("✅ [MICROSCOPE DEBUG] UDP command listener is ready on port \(self?.sendPort ?? 0) at \(elapsed)s")
                        self?.isListenerStarting = false
                    case .failed(let error):
                        print("❌ [MICROSCOPE DEBUG] UDP command listener failed at \(elapsed)s: \(error)")
                        self?.isListenerStarting = false
                    case .cancelled:
                        print("🚫 [MICROSCOPE DEBUG] UDP command listener cancelled at \(elapsed)s")
                        self?.isListenerStarting = false
                    @unknown default:
                        print("❓ [MICROSCOPE DEBUG] Unknown UDP listener state at \(elapsed)s")
                    }
                }

                self.listener?.newConnectionHandler = { [weak self] newConnection in
                    guard let self = self else { return }
                    print("New command connection established")
                    newConnection.start(queue: self.commandReceiveQueue)
                    self.receiveCommandData(on: newConnection)
                }

                self.listener?.start(queue: self.commandReceiveQueue)
            } catch {
                print("Failed to start UDP command listener: \(error)")
                self.isListenerStarting = false
            }
        }
    }

    private func receiveCommandData(on connection: NWConnection) {
        connection.receive(minimumIncompleteLength: 1, maximumLength: 64) { [weak self] data, context, isComplete, error in
            if let data = data, data.count > 4 {
                self?.handleCommandData(data)
            }

            if let error = error {
                print("Command connection error: \(error)")
                // Don't continue receiving on error
                connection.cancel()
                return
            }

            if isComplete {
                print("Command connection completed")
                connection.cancel()
                return
            }

            // Continue receiving only if no error and not complete
            self?.receiveCommandData(on: connection)
        }
    }

    private func handleCommandData(_ data: Data) {
        if let string = String(data: data, encoding: .utf8) {
            print("Received command string: \(string)")
            if string.hasPrefix("JHCMD") {
                if data.count > 6 && Int(data[6]) == 1 {
                    DispatchQueue.main.async { [weak self] in
                        self?.captureImageFromDevice()
                    }
                }
            }
        } else {
            print("Failed to convert command data to string.")
        }
    }

    private func captureImageFromDevice() {
        // This method is called when device tap is detected
        // It should work exactly like the capture button click
        print("Device tap detected - capturing image")

        // Play camera shutter sound
        playCameraShutterSound()

        // Notify Flutter that capture was triggered (same as manual capture)
        // This will set _shouldUploadNextImage = true in Flutter
        methodChannel?.invokeMethod("onDeviceCapture", arguments: nil)
    }

    private func playCameraShutterSound() {
        // Import AudioToolbox at the top if not already imported
        AudioServicesPlaySystemSound(SystemSoundID(1108)) // 1108 = camera shutter
    }

    func handleFrameData(_ data: Data) {
        let now = Date()

        // Track first frame timing
        if lastFrameReceivedTime == nil {
            print("🎬 [MICROSCOPE DEBUG] FIRST FRAME DATA RECEIVED at \(now)")
        }

        lastFrameReceivedTime = now
        hasReportedTimeout = false

        let frameIndex = Int(data[0]) + Int(data[1]) * 256
        let packetIndex = Int(data[3])
        let bufType = Int(data[4])

        // Detect new frame
        if frameIndex != currentFrameIndex {
            if !currentFrameData.isEmpty {
                processCompleteFrame()
            }

            // Log new frame detection
            if currentFrameIndex == -1 {
                print("🎬 [MICROSCOPE DEBUG] FIRST FRAME DETECTED: frameIndex=\(frameIndex)")
            }

            currentFrameIndex = frameIndex
            expectedPacketIndex = 0
            currentFrameData = Data()
        }
        
        // If packet is in sequence, append it
        if packetIndex == expectedPacketIndex {
            let payloadStartIndex: Int
            
            if bufType == 49 || packetIndex > 0 {
                payloadStartIndex = 8
            } else {
                payloadStartIndex = 24
            }
            
            // Verify data bounds to prevent crashes
            if data.count <= payloadStartIndex {
                return
            }
            
            let payloadData = data.subdata(in: payloadStartIndex..<data.count)
            currentFrameData.append(payloadData)
            expectedPacketIndex += 1
        } else {
            // Handle out of sequence packet - simple strategy
            if packetIndex < expectedPacketIndex {
                // Received earlier packet in current frame, ignoring
            } else {
                // We missed some packets, try to recover
                let payloadStartIndex: Int
                if bufType == 49 || packetIndex > 0 {
                    payloadStartIndex = 8
                } else {
                    payloadStartIndex = 24
                }
                
                // Verify data bounds to prevent crashes 
                if data.count <= payloadStartIndex {
                    return
                }
                
                let payloadData = data.subdata(in: payloadStartIndex..<data.count)
                currentFrameData.append(payloadData)
                expectedPacketIndex = packetIndex + 1
            }
        }
        
        // Send heartbeat every 50 frames
        if frameIndex % 50 == 0 {
            sendHeartbeat()
        }
    }

    private func processCompleteFrame() {
        frameCount += 1
        let now = Date()
        let timeInterval = now.timeIntervalSince(lastFrameProcessTime)
        lastFrameProcessTime = now

        // Log first frame processing
        if frameCount == 1 {
            print("🎬 [MICROSCOPE DEBUG] PROCESSING FIRST COMPLETE FRAME - size: \(currentFrameData.count) bytes")
        }

        // Check if the data has the expected format for an image
        if currentFrameData.count >= 4 {
            // Check for common image format headers
            let isJPEG = currentFrameData.count >= 3 && currentFrameData[0] == 0xFF && currentFrameData[1] == 0xD8 && currentFrameData[2] == 0xFF

            if !isJPEG {
                if frameCount == 1 {
                    print("❌ [MICROSCOPE DEBUG] FIRST FRAME IS NOT JPEG - header: \(currentFrameData.prefix(10).map { String(format: "%02X", $0) }.joined(separator: " "))")
                }
                currentFrameData = Data()
                return
            } else if frameCount == 1 {
                print("✅ [MICROSCOPE DEBUG] FIRST FRAME IS VALID JPEG")
            }
        }

        // Send JPEG data directly to Flutter for faster performance
        if currentFrameData.count > 0 {
            if frameCount == 1 {
                print("📤 [MICROSCOPE DEBUG] SENDING FIRST FRAME TO FLUTTER at \(now)")
            }
            methodChannel?.invokeMethod("onFrameReceived", arguments: FlutterStandardTypedData(bytes: currentFrameData))

            if frameCount == 1 {
                print("✅ [MICROSCOPE DEBUG] FIRST FRAME SENT TO FLUTTER")
            }
        }

        currentFrameData = Data()
    }
    
    private func sendInitialCommands() {
        let startTime = Date()
        print("📡 [MICROSCOPE DEBUG] Sending initial commands at \(startTime)")

        // Log command details
        let initCmd1 = Data([0x4A, 0x48, 0x43, 0x4D, 0x44, 0x10, 0x00])
        let initCmd2 = Data([0x4A, 0x48, 0x43, 0x4D, 0x44, 0x20, 0x00])

        print("📡 [MICROSCOPE DEBUG] Sending init command 1: \(initCmd1.map { String(format: "%02X", $0) }.joined(separator: " "))")
        sendCommand(initCmd1)

        print("📡 [MICROSCOPE DEBUG] Sending init command 2: \(initCmd2.map { String(format: "%02X", $0) }.joined(separator: " "))")
        sendCommand(initCmd2)

        print("💓 [MICROSCOPE DEBUG] Sending initial heartbeat")
        sendHeartbeat()

        print("📡 [MICROSCOPE DEBUG] All initial commands sent in \(Date().timeIntervalSince(startTime))s")
    }
    
    private func sendHeartbeat() {
        let heartbeatCmd = Data([0x4A, 0x48, 0x43, 0x4D, 0x44, 0xD0, 0x01])
        sendCommand(heartbeatCmd)
    }
    
    private func startHeartbeat() {
        heartbeatTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { [weak self] _ in
            self?.sendHeartbeat()
        }
    }
    
    private func startFrameTimeoutMonitor() {
        frameTimeoutTimer?.invalidate()
        frameTimeoutTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self, let lastFrameTime = self.lastFrameReceivedTime else { return }
            
            let elapsedTime = Date().timeIntervalSince(lastFrameTime)
            if elapsedTime > self.frameTimeoutInterval && !self.hasReportedTimeout {
                self.hasReportedTimeout = true
                self.methodChannel?.invokeMethod("onConnectionTimeout", arguments: nil)
                // Try to recover
                self.refreshConnection()
            }
        }
    }
    
    func sendCommand(_ data: Data) {
        connection?.send(content: data, completion: .contentProcessed({ error in
            // Silent completion handler
        }))
    }
    
    func captureImage() {
        sendCommand(Data([0x4A, 0x48, 0x43, 0x4D, 0x44, 0xD0, 0x02]))
    }
    
    deinit {
        heartbeatTimer?.invalidate()
        frameTimeoutTimer?.invalidate()
        connection?.cancel()
        listener?.cancel()

        let shutdownCmd = Data([0x4A, 0x48, 0x43, 0x4D, 0x44, 0xD0, 0x02])
        sendCommand(shutdownCmd)
    }
}

// Extension to help with debug logging
extension Data {
    var hexDescription: String {
        return self.map { String(format: "%02X", $0) }.joined(separator: " ")
    }
}
