import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:googleapis/storage/v1.dart' as gg_store;
import 'package:googleapis_auth/auth_io.dart';
import 'package:gt_plus/global_controller.dart';
import 'package:gt_plus/modules/microscope/view/microscope_preview_view.dart';
import 'package:gt_plus/modules/microscope/view/microscope_view.dart';
import 'package:gt_plus/modules/microscope/view/wifi_instruction_view.dart';
import 'package:gt_plus/modules/scanImages/controller/scan_images_controller.dart';
import 'package:gt_plus/services/api_service.dart';
import 'package:gt_plus/services/prefs_service.dart';
import 'package:gt_plus/services/sound_service.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
  
import '../../../models/clinic_details_model.dart';

class MicroscopeController extends GetxController {
  static const channel = MethodChannel('com.telehealth.microscope/channel');
  final _networkInfo = NetworkInfo();
  final GlobalController _globalController = Get.find<GlobalController>();

  final Rx<Uint8List?> imageData = Rx<Uint8List?>(null);
  final Rx<Uint8List?> savedImageData = Rx<Uint8List?>(null);
  final RxBool isConnected = RxBool(false);
  final RxBool _shouldUploadNextImage = RxBool(false);

  // Error handling properties
  final RxBool hasError = RxBool(false);
  final RxString errorMessage = RxString('');

  Timer? _connectionChecker;
  final _apiService = ApiService();
  final _prefsService = PrefsService();
  final _soundService = SoundService();
  RxBool isLoading = false.obs;

  // Debug timing
  DateTime? _controllerStartTime;
  DateTime? _connectionStartTime;

  // Flag to track if refresh is already in progress
  final RxBool _isRefreshing = RxBool(false);
  
  // Accessor for the view to check refresh status
  bool get isRefreshing => _isRefreshing.value;

  @override
  void onInit() {
    super.onInit();
    _controllerStartTime = DateTime.now();
    debugPrint("🚀 [FLUTTER DEBUG] MicroscopeController initialized at $_controllerStartTime");

    _setupMethodChannel();
    _initializeConnection();
    _startConnectionChecker();
  }

  void _startConnectionChecker() {
    _connectionChecker = Timer.periodic(const Duration(seconds: 2), (_) async {
      final wifiName = await _getWifiName();
      if (wifiName != null) {
        // Remove all non-alphabetical characters and convert to lowercase
        final normalizedWifiName = wifiName.toLowerCase().replaceAll(RegExp(r'[^a-z]'), '');
        if (normalizedWifiName.contains('maxsee')) {
          if (!isConnected.value) _initializeConnection();
          return;
        }
      }
      
      // Not connected to microscope network
      if (Get.currentRoute == MicroscopeView.routeName) {
        Get.offNamed(WifiInstructionView.routeName);
      }
      isConnected.value = false;
    });
  }

  Future<String?> _getWifiName() async => await _networkInfo.getWifiName();

  @override
  void onClose() {
    _connectionChecker?.cancel();
    super.onClose();
  }

  void _initializeConnection() async {
    try {
      _connectionStartTime = DateTime.now();
      debugPrint("🔄 [FLUTTER DEBUG] Starting connection initialization at $_connectionStartTime");

      if (_controllerStartTime != null) {
        final timeSinceControllerStart = _connectionStartTime!.difference(_controllerStartTime!).inMilliseconds;
        debugPrint("🔄 [FLUTTER DEBUG] Connection started ${timeSinceControllerStart}ms after controller init");
      }

      _clearError();
      await channel.invokeMethod('refreshConnection');

      final elapsed = DateTime.now().difference(_connectionStartTime!).inMilliseconds;
      debugPrint("✅ [FLUTTER DEBUG] Connection initialized in ${elapsed}ms");

      isConnected.value = true;
    } on PlatformException catch (e) {
      debugPrint("❌ [FLUTTER DEBUG] Connection error: ${e.message}");
      _setError("Connection error: ${e.message ?? 'Unknown error'}");
    } catch (e) {
      debugPrint("❌ [FLUTTER DEBUG] General error: $e");
      _setError("Failed to initialize connection: $e");
    }
  }

  void _setupMethodChannel() {
    channel.setMethodCallHandler((call) async {
      if (call.method == 'onFrameReceived') {
        final data = call.arguments as Uint8List;
        _updateImage(data);
      } else if (call.method == 'onConnectionTimeout') {
        _handleConnectionTimeout();
      } else if (call.method == 'onDeviceCapture') {
        _handleDeviceCapture();
      }
      return null;
    });
  }

  void resetImage() {
    imageData.value = null;
    // Don't clear savedImageData here either
    _shouldUploadNextImage.value = false;
  }

  void _updateImage(Uint8List data) {
    try {
      final now = DateTime.now();
      _clearError();
      // The native side now sends JPEG, so the PNG header check is removed.

      // If we've been waiting for an image for too long, reset the loading state
      if (imageData.value == null) {
        debugPrint("🎬 [FLUTTER DEBUG] FIRST FRAME RECEIVED at $now - size: ${data.length} bytes");

        // Calculate timing from various start points
        if (_controllerStartTime != null) {
          final totalTime = now.difference(_controllerStartTime!).inMilliseconds;
          debugPrint("🎬 [FLUTTER DEBUG] Total time from controller init to first frame: ${totalTime}ms");
        }

        if (_connectionStartTime != null) {
          final connectionTime = now.difference(_connectionStartTime!).inMilliseconds;
          debugPrint("🎬 [FLUTTER DEBUG] Time from connection start to first frame: ${connectionTime}ms");
        }

        // Log device info for debugging
        debugPrint("🎬 [FLUTTER DEBUG] Device info - Platform: ${Platform.operatingSystem} ${Platform.operatingSystemVersion}");
      }

      imageData.value = data;
      if (_shouldUploadNextImage.value) {
        savedImageData.value = data;
        _shouldUploadNextImage.value = false;
        Get.toNamed(MicroscopePreviewView.routeName);
      }
    } catch (error) {
      debugPrint("❌ [FLUTTER DEBUG] Error processing image data: $error");
      _setError("Failed to process image data: $error");
    }
  }

  // Error handling methods
  void _setError(String message) {
    hasError.value = true;
    errorMessage.value = message;
    debugPrint("Microscope Error: $message");
  }

  void _clearError() {
    hasError.value = false;
    errorMessage.value = '';
  }

  void logImageError(Object error, StackTrace? stackTrace) {
    final errorMsg = "Image rendering error: $error";
    debugPrint(errorMsg);
    if (stackTrace != null) {
      debugPrint(stackTrace.toString());
    }
    _setError(errorMsg);
  }

  Future<bool> uploadImage() async {
    try {
      isLoading.value = true;

      if (_globalController.isTestMode.value){
        isLoading.value = false;
        return true;
      }
      if (savedImageData.value != null) {
        ClinicDetailsModel? clinicDetails =
            await _prefsService.getClinicDetails();
        String identifier = await _prefsService.getIdentifier();

        String bucketName =
            (clinicDetails?.clinicName ?? 'default').toLowerCase();
        String fileName =
            await _getFormattedFileName(suffix: 'skin', extension: 'jpeg');
        String gcsPath = '$identifier/$fileName';

        final credentials = await _loadServiceAccountCredentials();
        final httpClient = await clientViaServiceAccount(
          credentials,
          [gg_store.StorageApi.devstorageFullControlScope],
        );

        final storage = gg_store.StorageApi(httpClient);
        final media = gg_store.Media(
          Stream.value(savedImageData.value!),
          savedImageData.value!.length,
          contentType: 'image/jpeg',
        );

        final response = await storage.objects.insert(
          gg_store.Object(name: gcsPath),
          bucketName,
          uploadMedia: media,
        );

        if (response.id == null) {
          return false;
        }

        return await _postImageToApi('$bucketName/$gcsPath');
      } else {
        reusableSnackBar(message: "Can not find image to upload");
        return false;
      }
    } catch (e) {
      debugPrint("Error uploading image: $e");
      reusableSnackBar(message: "Failed to upload image to cloud");
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<String> _getFormattedFileName({
    required String suffix,
    required String extension,
  }) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final platform = Theme.of(Get.context!).platform == TargetPlatform.iOS
        ? 'ios'
        : 'android';
    final packageInfo = await PackageInfo.fromPlatform();
    final appVersion = 'v${packageInfo.version}';
    return '${timestamp}_${platform}_${appVersion}_$suffix.$extension';
  }

  Future<ServiceAccountCredentials> _loadServiceAccountCredentials() async {
    String jsonContent =
        await rootBundle.loadString('assets/gCloud/credentials.json');
    final Map<String, dynamic> jsonMap = jsonDecode(jsonContent);
    return ServiceAccountCredentials.fromJson(jsonMap);
  }

  Future<bool> _postImageToApi(String imagePath) async {
    try {
      final success = await _apiService.postImage(
        type: ScanImageType.skinImage.title,
        path: imagePath,
      );
      if (success) {
        return true;
      } else {
        reusableSnackBar(message: "Something went wrong.");
        return false;
      }
    } catch (e) {
      debugPrint("Error posting to API: $e");
      reusableSnackBar(message: "Something went wrong.");
      return false;
    }
  }

  void _handleConnectionTimeout() {
    debugPrint("Connection timeout detected");
    // Only show error if we're currently in the MicroscopeView
    if (!hasError.value && Get.currentRoute == MicroscopeView.routeName) {
      _setError("Connection timeout. The microscope is not responding.");

      // Auto-retry after a short delay
      Future.delayed(const Duration(seconds: 3), () {
        if (hasError.value && errorMessage.value.contains('timeout')) {
          refreshConnection();
        }
      });
    }
  }

  void _handleDeviceCapture() {
    debugPrint("Device capture detected - same as manual capture");
    // This should work exactly like the manual capture button
    // Set the flag to capture the next image and navigate to preview
    _shouldUploadNextImage.value = true;
    // The next frame received will automatically be saved and navigate to preview
  }

  // Method to be called when entering MicroscopeView
  void resetForMicroscopeView() {
    // Reset image state but preserve saved image data
    imageData.value = null;
    // Don't reset savedImageData here as it's needed for uploading
    hasError.value = false;
    errorMessage.value = '';
    
    // Force reconnection
    isConnected.value = false;
    _initializeConnection();
  }

  void refreshConnection() {
    // Prevent multiple simultaneous refresh attempts
    if (_isRefreshing.value) {
      debugPrint("Refresh already in progress, ignoring request");
      return;
    }
    
    _isRefreshing.value = true;
    debugPrint("Starting complete connection refresh");
    
    // Clear current state
    imageData.value = null;
    isConnected.value = false;
    _clearError();
    
    // Try to completely reset native connection
    try {
      channel.invokeMethod('completeReset');
    } catch (e) {
      debugPrint("Error calling complete reset: $e");
    }
    
    // Add a longer delay to ensure native side has time to completely reset
    Future.delayed(const Duration(seconds: 1), () {
      try {
        _initializeConnection();
      } catch (e) {
        debugPrint("Error reinitializing connection: $e");
        _setError("Failed to reinitialize connection: $e");
      } finally {
        _isRefreshing.value = false;
      }
    });
  }

  void captureImage() async {
    try {
      _shouldUploadNextImage.value = true;
      _soundService.playShutterSound();
      await channel.invokeMethod('captureImage');
    } on PlatformException catch (e) {
      debugPrint("Failed to capture image: ${e.message}");
      _shouldUploadNextImage.value = false;
      _setError("Failed to capture image: ${e.message ?? 'Unknown error'}");
    } catch (e) {
      debugPrint("Failed to capture image: $e");
      _shouldUploadNextImage.value = false;
      _setError("Failed to capture image: $e");
    }
  }

  // Method to explicitly verify we have image data before navigating to AfterMicroscopeScreen
  bool hasSavedImageData() {
    if (savedImageData.value == null) {
      reusableSnackBar(message: "No microscope image captured. Please capture an image first.");
      return false;
    }
    return true;
  }
}
